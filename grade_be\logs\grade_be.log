2025-07-23 20:23:16,145 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-23 20:25:40,249 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-23 20:25:40,250 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-23 20:25:40,349 [INFO] django.server: "GET /media/profile_pictures/sq-google-g-logo-update_dezeen_2364_col_0-852x852.jpg HTTP/1.1" 304 0
2025-07-23 20:25:40,405 [WARNING] django.server: "GET /api/user/credits/ HTTP/1.1" 401 183
2025-07-23 20:25:40,419 [WARNING] django.server: "GET /api/user/credits/ HTTP/1.1" 401 183
2025-07-23 20:25:40,527 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 20:25:40,545 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 20:25:59,698 [INFO] django.server: "OPTIONS /api/organization/students/ HTTP/1.1" 200 0
2025-07-23 20:25:59,704 [INFO] django.server: "OPTIONS /api/organization/students/pending_invitations/ HTTP/1.1" 200 0
2025-07-23 20:25:59,712 [INFO] django.server: "OPTIONS /api/organization/hierarchy-levels/ HTTP/1.1" 200 0
2025-07-23 20:25:59,725 [INFO] django.server: "OPTIONS /api/organization/students/ HTTP/1.1" 200 0
2025-07-23 20:25:59,751 [INFO] django.server: "OPTIONS /api/organization/hierarchy-levels/ HTTP/1.1" 200 0
2025-07-23 20:25:59,751 [INFO] django.server: "OPTIONS /api/organization/students/pending_invitations/ HTTP/1.1" 200 0
2025-07-23 20:26:00,023 [WARNING] django.server: "GET /api/organization/students/ HTTP/1.1" 401 183
2025-07-23 20:26:00,029 [WARNING] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 401 183
2025-07-23 20:26:00,039 [WARNING] django.server: "GET /api/organization/students/ HTTP/1.1" 401 183
2025-07-23 20:26:00,040 [WARNING] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 401 183
2025-07-23 20:26:00,049 [WARNING] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 401 183
2025-07-23 20:26:00,055 [WARNING] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 401 183
2025-07-23 20:26:03,153 [WARNING] django.server: "GET /api/user/credits/ HTTP/1.1" 401 183
2025-07-23 20:26:03,155 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 20:26:03,166 [WARNING] django.server: "GET /api/user/credits/ HTTP/1.1" 401 183
2025-07-23 20:26:03,173 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 20:26:09,731 [INFO] django.server: "OPTIONS /api/login/ HTTP/1.1" 200 0
2025-07-23 20:26:09,751 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-23 20:26:10,595 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-23 20:26:10,611 [INFO] authentication.views: Roles for user 3: ['organization']
2025-07-23 20:26:10,614 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 1011
2025-07-23 20:26:11,862 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 20:26:11,877 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 20:26:11,879 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:26:11,890 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:26:15,465 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:26:15,479 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:26:23,059 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:26:23,067 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:26:26,301 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-23 20:26:26,305 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-23 20:26:26,306 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-23 20:26:26,324 [INFO] django.server: "OPTIONS /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 0
2025-07-23 20:26:26,347 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-23 20:26:26,350 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-23 20:26:26,361 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-23 20:26:26,368 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-23 20:26:26,391 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-23 20:26:27,831 [INFO] django.server: "OPTIONS /api/organization/progress_summary/ HTTP/1.1" 200 0
2025-07-23 20:26:27,832 [INFO] django.server: "OPTIONS /api/organization/progress_summary/ HTTP/1.1" 200 0
2025-07-23 20:26:27,865 [INFO] django.server: "GET /api/organization/progress_summary/ HTTP/1.1" 200 391
2025-07-23 20:26:27,894 [INFO] django.server: "GET /api/organization/progress_summary/ HTTP/1.1" 200 391
2025-07-23 20:26:29,826 [INFO] django.server: "OPTIONS /api/organization/tests/ HTTP/1.1" 200 0
2025-07-23 20:26:29,829 [INFO] django.server: "OPTIONS /api/organization/tests/ HTTP/1.1" 200 0
2025-07-23 20:26:29,840 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-23 20:26:29,851 [INFO] django.server: "GET /api/organization/tests/ HTTP/1.1" 200 343
2025-07-23 20:26:29,869 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-23 20:26:29,872 [INFO] django.server: "GET /api/organization/tests/ HTTP/1.1" 200 343
2025-07-23 20:26:30,703 [INFO] django.server: "OPTIONS /api/grade/get-evaluators/ HTTP/1.1" 200 0
2025-07-23 20:26:30,704 [INFO] django.server: "OPTIONS /api/grade/get-evaluators/ HTTP/1.1" 200 0
2025-07-23 20:26:30,720 [INFO] django.server: "GET /api/grade/get-evaluators/ HTTP/1.1" 200 2
2025-07-23 20:26:30,734 [INFO] django.server: "OPTIONS /api/grade/unassigned-answers/ HTTP/1.1" 200 0
2025-07-23 20:26:30,742 [INFO] django.server: "GET /api/grade/get-evaluators/ HTTP/1.1" 200 2
2025-07-23 20:26:30,752 [INFO] django.server: "GET /api/grade/unassigned-answers/ HTTP/1.1" 200 2
2025-07-23 20:26:30,761 [INFO] django.server: "GET /api/grade/unassigned-answers/ HTTP/1.1" 200 2
2025-07-23 20:26:31,483 [INFO] django.server: "GET /api/organization/tests/ HTTP/1.1" 200 343
2025-07-23 20:26:31,508 [INFO] django.server: "GET /api/organization/tests/ HTTP/1.1" 200 343
2025-07-23 20:26:32,310 [INFO] django.server: "OPTIONS /api/organization/hierarchy-levels/tree/ HTTP/1.1" 200 0
2025-07-23 20:26:32,310 [INFO] django.server: "OPTIONS /api/organization/hierarchy-levels/tree/ HTTP/1.1" 200 0
2025-07-23 20:26:32,323 [INFO] django.server: "GET /api/organization/hierarchy-levels/tree/ HTTP/1.1" 200 30
2025-07-23 20:26:32,340 [INFO] django.server: "GET /api/organization/hierarchy-levels/tree/ HTTP/1.1" 200 30
2025-07-23 20:26:33,210 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 20:26:33,213 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:26:33,228 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 20:26:33,232 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:26:38,904 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 20:26:38,915 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 20:26:42,196 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-23 20:26:42,211 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-23 20:26:42,233 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-23 20:26:42,236 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-23 20:26:42,237 [INFO] django.server: "GET /api/organization/hierarchy-levels/ HTTP/1.1" 200 2
2025-07-23 20:26:42,245 [INFO] django.server: "GET /api/organization/students/ HTTP/1.1" 200 195
2025-07-23 20:26:42,263 [INFO] django.server: "GET /api/organization/students/pending_invitations/ HTTP/1.1" 200 96
2025-07-23 20:26:42,267 [INFO] django.server: "GET /api/organization/student-hierarchies/student_hierarchies/?student_id=1 HTTP/1.1" 200 96
2025-07-23 20:26:46,718 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 20:26:46,718 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:26:46,734 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 20:26:46,735 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:26:49,001 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:26:49,016 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:36:42,480 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:37:31,475 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:37:43,464 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:38:46,467 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:38:56,483 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:39:05,467 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:39:21,498 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:39:21,508 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:39:40,475 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:39:40,487 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:40:08,463 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:43:08,197 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-23 20:44:16,848 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:44:16,854 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:44:17,118 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 20:44:17,123 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 20:44:21,801 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-23 20:44:22,733 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-23 20:44:22,750 [INFO] authentication.views: Roles for user 3: ['organization']
2025-07-23 20:44:22,754 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 1011
2025-07-23 20:44:27,168 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 20:44:27,181 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:44:27,185 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 20:44:27,199 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:44:30,195 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 20:44:30,211 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:34:56,494 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-23 21:36:56,971 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-23 21:36:56,972 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-23 21:36:57,106 [WARNING] django.server: "GET /api/user/credits/ HTTP/1.1" 401 183
2025-07-23 21:36:57,120 [WARNING] django.server: "GET /api/user/credits/ HTTP/1.1" 401 183
2025-07-23 21:36:57,215 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 21:36:57,229 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 21:37:17,617 [INFO] django.server: "OPTIONS /api/login/ HTTP/1.1" 200 0
2025-07-23 21:37:17,639 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-23 21:37:18,979 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-23 21:37:19,001 [INFO] authentication.views: Roles for user 3: ['organization']
2025-07-23 21:37:19,006 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 1011
2025-07-23 21:37:19,439 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 21:37:19,459 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:37:19,480 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 21:37:19,495 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:37:24,320 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:37:24,339 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:40:56,854 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:40:56,872 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:41:19,252 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:46:21,496 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-23 21:46:48,598 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:46:48,608 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:46:48,811 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 21:46:48,830 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 21:46:52,980 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-23 21:46:53,613 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-23 21:46:53,630 [INFO] authentication.views: Roles for user 3: ['organization']
2025-07-23 21:46:53,633 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 1011
2025-07-23 21:46:54,039 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 21:46:54,076 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 21:46:54,090 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:46:54,106 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:46:59,138 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:46:59,151 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:48:54,200 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-23 21:51:31,188 [INFO] django.server: "OPTIONS /api/login/ HTTP/1.1" 200 0
2025-07-23 21:51:31,202 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: True
2025-07-23 21:51:33,623 [INFO] authentication.views: Authenticated user: None
2025-07-23 21:51:33,649 [WARNING] django.server: "POST /api/login/ HTTP/1.1" 401 61
2025-07-23 21:51:38,747 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: True
2025-07-23 21:51:41,113 [INFO] authentication.views: Authenticated user: None
2025-07-23 21:51:41,140 [WARNING] django.server: "POST /api/login/ HTTP/1.1" 401 61
2025-07-23 21:51:55,129 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-23 21:51:57,474 [INFO] authentication.views: Authenticated user: None
2025-07-23 21:51:57,497 [WARNING] django.server: "POST /api/login/ HTTP/1.1" 401 61
2025-07-23 21:52:02,715 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-23 21:52:05,094 [INFO] authentication.views: Authenticated user: None
2025-07-23 21:52:05,400 [WARNING] django.server: "POST /api/login/ HTTP/1.1" 401 61
2025-07-23 21:52:37,895 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-23 21:52:40,437 [INFO] authentication.views: Authenticated user: None
2025-07-23 21:52:40,463 [WARNING] django.server: "POST /api/login/ HTTP/1.1" 401 61
2025-07-23 21:52:51,134 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-23 21:52:53,804 [INFO] authentication.views: Authenticated user: None
2025-07-23 21:52:53,836 [WARNING] django.server: "POST /api/login/ HTTP/1.1" 401 61
2025-07-23 21:53:27,651 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-07-23 21:53:56,181 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-23 21:53:56,181 [INFO] django.server: "OPTIONS /api/user/credits/ HTTP/1.1" 200 0
2025-07-23 21:53:56,349 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:53:56,365 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:53:56,409 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 21:53:56,418 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 21:54:05,656 [INFO] django.server: "OPTIONS /api/login/ HTTP/1.1" 200 0
2025-07-23 21:54:05,674 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: True
2025-07-23 21:54:06,939 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-23 21:54:06,956 [INFO] authentication.views: Roles for user 2: ['qp_uploader', 'admin']
2025-07-23 21:54:06,958 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 930
2025-07-23 21:54:07,499 [INFO] django.server: "OPTIONS /api/user-count/ HTTP/1.1" 200 0
2025-07-23 21:54:07,502 [INFO] django.server: "OPTIONS /api/user-role-counts/ HTTP/1.1" 200 0
2025-07-23 21:54:07,511 [INFO] django.server: "OPTIONS /api/organization/list/ HTTP/1.1" 200 0
2025-07-23 21:54:07,516 [INFO] django.server: "OPTIONS /api/grade/main-requests/ HTTP/1.1" 200 0
2025-07-23 21:54:07,520 [INFO] django.server: "OPTIONS /api/feedback/list/ HTTP/1.1" 200 0
2025-07-23 21:54:07,530 [INFO] django.server: "OPTIONS /api/user-count/ HTTP/1.1" 200 0
2025-07-23 21:54:07,535 [INFO] django.server: "OPTIONS /api/user-role-counts/ HTTP/1.1" 200 0
2025-07-23 21:54:07,544 [INFO] django.server: "OPTIONS /api/organization/list/ HTTP/1.1" 200 0
2025-07-23 21:54:07,548 [INFO] django.server: "OPTIONS /api/grade/main-requests/ HTTP/1.1" 200 0
2025-07-23 21:54:07,552 [INFO] django.server: "OPTIONS /api/feedback/list/ HTTP/1.1" 200 0
2025-07-23 21:54:07,858 [INFO] django.server: "GET /api/user-count/ HTTP/1.1" 200 11
2025-07-23 21:54:07,900 [INFO] django.server: "GET /api/grade/main-requests/ HTTP/1.1" 200 2
2025-07-23 21:54:07,901 [INFO] django.server: "GET /api/user-role-counts/ HTTP/1.1" 200 71
2025-07-23 21:54:07,910 [INFO] django.server: "GET /api/organization/list/ HTTP/1.1" 200 30
2025-07-23 21:54:07,919 [INFO] django.server: "GET /api/feedback/list/ HTTP/1.1" 200 21
2025-07-23 21:54:07,944 [INFO] django.server: "GET /api/user-count/ HTTP/1.1" 200 11
2025-07-23 21:54:07,947 [INFO] django.server: "OPTIONS /api/grade/get-evaluators/ HTTP/1.1" 200 0
2025-07-23 21:54:07,996 [INFO] django.server: "GET /api/grade/main-requests/ HTTP/1.1" 200 2
2025-07-23 21:54:08,001 [INFO] django.server: "GET /api/feedback/list/ HTTP/1.1" 200 21
2025-07-23 21:54:08,001 [INFO] django.server: "GET /api/user-role-counts/ HTTP/1.1" 200 71
2025-07-23 21:54:08,008 [INFO] django.server: "GET /api/grade/get-evaluators/ HTTP/1.1" 200 2
2025-07-23 21:54:08,008 [INFO] django.server: "GET /api/organization/list/ HTTP/1.1" 200 30
2025-07-23 21:54:08,024 [INFO] django.server: "OPTIONS /api/grade/unassigned-answers/ HTTP/1.1" 200 0
2025-07-23 21:54:08,043 [INFO] django.server: "GET /api/grade/get-evaluators/ HTTP/1.1" 200 2
2025-07-23 21:54:08,049 [INFO] django.server: "GET /api/grade/unassigned-answers/ HTTP/1.1" 200 2
2025-07-23 21:54:08,066 [INFO] django.server: "GET /api/grade/unassigned-answers/ HTTP/1.1" 200 2
2025-07-23 21:54:11,116 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:54:11,131 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:54:21,114 [INFO] django.server: "GET /api/feedback/list/ HTTP/1.1" 200 21
2025-07-23 21:54:21,116 [INFO] django.server: "GET /api/grade/main-requests/ HTTP/1.1" 200 2
2025-07-23 21:54:21,118 [INFO] django.server: "GET /api/organization/list/ HTTP/1.1" 200 30
2025-07-23 21:54:21,130 [INFO] django.server: "GET /api/user-count/ HTTP/1.1" 200 11
2025-07-23 21:54:21,132 [INFO] django.server: "GET /api/user-role-counts/ HTTP/1.1" 200 71
2025-07-23 21:54:21,152 [INFO] django.server: "GET /api/feedback/list/ HTTP/1.1" 200 21
2025-07-23 21:54:21,157 [INFO] django.server: "GET /api/organization/list/ HTTP/1.1" 200 30
2025-07-23 21:54:21,160 [INFO] django.server: "GET /api/grade/main-requests/ HTTP/1.1" 200 2
2025-07-23 21:54:21,169 [INFO] django.server: "GET /api/grade/get-evaluators/ HTTP/1.1" 200 2
2025-07-23 21:54:21,170 [INFO] django.server: "GET /api/user-role-counts/ HTTP/1.1" 200 71
2025-07-23 21:54:21,174 [INFO] django.server: "GET /api/user-count/ HTTP/1.1" 200 11
2025-07-23 21:54:21,192 [INFO] django.server: "GET /api/grade/unassigned-answers/ HTTP/1.1" 200 2
2025-07-23 21:54:21,196 [INFO] django.server: "GET /api/grade/get-evaluators/ HTTP/1.1" 200 2
2025-07-23 21:54:21,229 [INFO] django.server: "GET /api/grade/unassigned-answers/ HTTP/1.1" 200 2
2025-07-23 21:54:23,978 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:54:23,995 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:54:29,367 [INFO] django.server: "GET /api/grade/main-requests/ HTTP/1.1" 200 2
2025-07-23 21:54:29,369 [INFO] django.server: "GET /api/user-count/ HTTP/1.1" 200 11
2025-07-23 21:54:29,373 [INFO] django.server: "GET /api/feedback/list/ HTTP/1.1" 200 21
2025-07-23 21:54:29,378 [INFO] django.server: "GET /api/organization/list/ HTTP/1.1" 200 30
2025-07-23 21:54:29,388 [INFO] django.server: "GET /api/user-role-counts/ HTTP/1.1" 200 71
2025-07-23 21:54:29,391 [INFO] django.server: "GET /api/user-count/ HTTP/1.1" 200 11
2025-07-23 21:54:29,421 [INFO] django.server: "GET /api/grade/main-requests/ HTTP/1.1" 200 2
2025-07-23 21:54:29,424 [INFO] django.server: "GET /api/feedback/list/ HTTP/1.1" 200 21
2025-07-23 21:54:29,430 [INFO] django.server: "GET /api/organization/list/ HTTP/1.1" 200 30
2025-07-23 21:54:29,432 [INFO] django.server: "GET /api/grade/get-evaluators/ HTTP/1.1" 200 2
2025-07-23 21:54:29,439 [INFO] django.server: "GET /api/user-role-counts/ HTTP/1.1" 200 71
2025-07-23 21:54:29,450 [INFO] django.server: "GET /api/grade/unassigned-answers/ HTTP/1.1" 200 2
2025-07-23 21:54:29,457 [INFO] django.server: "GET /api/grade/get-evaluators/ HTTP/1.1" 200 2
2025-07-23 21:54:29,498 [INFO] django.server: "GET /api/grade/unassigned-answers/ HTTP/1.1" 200 2
2025-07-23 21:54:34,740 [INFO] django.server: "GET /api/grade/checkpermission/?email=eswaredits13%40gmail.com&role=qp_uploader HTTP/1.1" 200 74
2025-07-23 21:54:34,753 [INFO] django.server: "GET /api/grade/checkpermission/?email=eswaredits13%40gmail.com&role=qp_uploader HTTP/1.1" 200 74
2025-07-23 21:54:34,757 [INFO] django.server: "GET /api/grade/checksubmission/?email=eswaredits13%40gmail.com&role=qp_uploader HTTP/1.1" 200 19
2025-07-23 21:54:34,771 [INFO] django.server: "GET /api/grade/checksubmission/?email=eswaredits13%40gmail.com&role=qp_uploader HTTP/1.1" 200 19
2025-07-23 21:54:39,177 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:54:39,192 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:54:46,088 [INFO] django.server: "GET /api/grade/checkpermission/?email=eswaredits13%40gmail.com&role=qp_uploader HTTP/1.1" 200 74
2025-07-23 21:54:46,109 [INFO] django.server: "GET /api/grade/checkpermission/?email=eswaredits13%40gmail.com&role=qp_uploader HTTP/1.1" 200 74
2025-07-23 21:54:46,112 [INFO] django.server: "GET /api/grade/checksubmission/?email=eswaredits13%40gmail.com&role=qp_uploader HTTP/1.1" 200 19
2025-07-23 21:54:46,128 [INFO] django.server: "GET /api/grade/checksubmission/?email=eswaredits13%40gmail.com&role=qp_uploader HTTP/1.1" 200 19
2025-07-23 21:55:11,312 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-23 21:55:13,766 [INFO] authentication.views: Authenticated user: None
2025-07-23 21:55:13,796 [WARNING] django.server: "POST /api/login/ HTTP/1.1" 401 61
2025-07-23 21:55:21,321 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-23 21:55:23,718 [INFO] authentication.views: Authenticated user: None
2025-07-23 21:55:23,741 [WARNING] django.server: "POST /api/login/ HTTP/1.1" 401 61
2025-07-23 21:55:28,180 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-23 21:55:30,936 [INFO] authentication.views: Authenticated user: None
2025-07-23 21:55:30,956 [WARNING] django.server: "POST /api/login/ HTTP/1.1" 401 61
2025-07-23 21:55:35,073 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-23 21:55:36,352 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-23 21:55:36,372 [INFO] authentication.views: Roles for user 3: ['organization']
2025-07-23 21:55:36,376 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 1011
2025-07-23 21:55:36,792 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 21:55:36,818 [INFO] django.server: "GET /api/questions/ HTTP/1.1" 200 2
2025-07-23 21:55:36,824 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:55:36,846 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:55:39,729 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:55:39,750 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:56:51,362 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: True
2025-07-23 21:56:52,610 [INFO] authentication.views: Authenticated user: <EMAIL>
2025-07-23 21:56:52,633 [INFO] authentication.views: Roles for user 2: ['qp_uploader', 'admin']
2025-07-23 21:56:52,639 [INFO] django.server: "POST /api/login/ HTTP/1.1" 200 930
2025-07-23 21:56:53,387 [INFO] django.server: "GET /api/user-count/ HTTP/1.1" 200 11
2025-07-23 21:56:53,400 [INFO] django.server: "GET /api/grade/main-requests/ HTTP/1.1" 200 2
2025-07-23 21:56:53,403 [INFO] django.server: "GET /api/organization/list/ HTTP/1.1" 200 30
2025-07-23 21:56:53,409 [INFO] django.server: "GET /api/user-role-counts/ HTTP/1.1" 200 71
2025-07-23 21:56:53,408 [INFO] django.server: "GET /api/feedback/list/ HTTP/1.1" 200 21
2025-07-23 21:56:53,423 [INFO] django.server: "GET /api/user-count/ HTTP/1.1" 200 11
2025-07-23 21:56:53,437 [INFO] django.server: "GET /api/grade/main-requests/ HTTP/1.1" 200 2
2025-07-23 21:56:53,469 [INFO] django.server: "GET /api/grade/get-evaluators/ HTTP/1.1" 200 2
2025-07-23 21:56:53,469 [INFO] django.server: "GET /api/organization/list/ HTTP/1.1" 200 30
2025-07-23 21:56:53,471 [INFO] django.server: "GET /api/feedback/list/ HTTP/1.1" 200 21
2025-07-23 21:56:53,477 [INFO] django.server: "GET /api/user-role-counts/ HTTP/1.1" 200 71
2025-07-23 21:56:53,499 [INFO] django.server: "GET /api/grade/unassigned-answers/ HTTP/1.1" 200 2
2025-07-23 21:56:53,502 [INFO] django.server: "GET /api/grade/get-evaluators/ HTTP/1.1" 200 2
2025-07-23 21:56:53,526 [INFO] django.server: "GET /api/grade/unassigned-answers/ HTTP/1.1" 200 2
2025-07-23 21:56:58,330 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:56:58,351 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:57:02,592 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:57:02,608 [INFO] django.server: "GET /api/user/credits/ HTTP/1.1" 200 122
2025-07-23 21:57:29,580 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-23 21:57:32,310 [INFO] authentication.views: Authenticated user: None
2025-07-23 21:57:32,330 [WARNING] django.server: "POST /api/login/ HTTP/1.1" 401 61
2025-07-23 21:57:37,574 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-23 21:57:39,977 [INFO] authentication.views: Authenticated user: None
2025-07-23 21:57:40,002 [WARNING] django.server: "POST /api/login/ HTTP/1.1" 401 61
2025-07-23 21:59:03,018 [INFO] authentication.views: Login attempt for email: <EMAIL>, is_admin: False
2025-07-23 21:59:05,503 [INFO] authentication.views: Authenticated user: None
2025-07-23 21:59:05,548 [WARNING] django.server: "POST /api/login/ HTTP/1.1" 401 61
2025-07-23 21:59:12,955 [INFO] django.server: "OPTIONS /api/forgot-password/ HTTP/1.1" 200 0
2025-07-23 21:59:34,268 [ERROR] authentication.views: Forgot password error: [WinError 10060] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond
2025-07-23 21:59:34,270 [WARNING] django.server: "POST /api/forgot-password/ HTTP/1.1" 400 244
2025-07-23 22:07:16,998 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
